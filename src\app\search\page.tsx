import { db } from '@/lib/prisma-db';
import Question<PERSON><PERSON> from '@/components/Question-Card';
import { auth } from '@clerk/nextjs/server';
import { Search } from 'lucide-react';

interface SearchPageProps {
  searchParams: Promise<{ q?: string }>;
}

export default async function SearchPage({ searchParams }: SearchPageProps) {
  const { q: searchTerm } = await searchParams;
  const { userId } = await auth();
  
  let currentUser = null;
  if (userId) {
    currentUser = await db.getOrCreateUser(userId);
  }

  let questions: any[] = [];
  let hasSearched = false;

  if (searchTerm && searchTerm.trim()) {
    hasSearched = true;
    questions = await db.searchQuestions(searchTerm.trim());
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4 flex items-center gap-2">
          <Search className="h-8 w-8" />
          Search Results
        </h1>
        
        {hasSearched && (
          <p className="text-zinc-600 dark:text-zinc-400">
            {questions.length > 0 
              ? `Found ${questions.length} question${questions.length === 1 ? '' : 's'} for "${searchTerm}"`
              : `No questions found for "${searchTerm}"`
            }
          </p>
        )}
        
        {!hasSearched && (
          <p className="text-zinc-600 dark:text-zinc-400">
            Use the search bar above to find questions by title, content, or tags.
          </p>
        )}
      </div>

      {/* Search Results */}
      {hasSearched && (
        <div className="space-y-4">
          {questions.length === 0 ? (
            <div className="text-center py-12">
              <Search className="h-16 w-16 mx-auto text-zinc-400 mb-4" />
              <p className="text-zinc-500 dark:text-zinc-400 text-lg mb-2">
                No questions found
              </p>
              <p className="text-zinc-400 dark:text-zinc-500">
                Try different keywords or check your spelling
              </p>
            </div>
          ) : (
            questions.map((q) => (
              <QuestionCard
                key={q.id}
                id={q.id}
                title={q.title}
                description={q.description}
                createdAt={q.createdAt.toISOString()}
                author={q.user}
                tags={q.tags}
                answersCount={q.answers.length}
                canDelete={currentUser?.id === q.userId}
              />
            ))
          )}
        </div>
      )}
    </div>
  );
}
