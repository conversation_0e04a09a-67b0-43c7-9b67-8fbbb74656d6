{"name": "stack-it", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@clerk/nextjs": "^6.24.0", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.1", "@radix-ui/react-slot": "^1.2.3", "@tiptap/extension-placeholder": "^2.26.1", "@tiptap/pm": "^2.26.1", "@tiptap/react": "^2.26.1", "@tiptap/starter-kit": "^2.26.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "next": "15.3.5", "prisma": "^6.11.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "react-select": "^5.10.2", "svix": "^1.69.0", "tailwind-variants": "^1.0.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/date-fns": "^2.5.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}, "prisma": {"seed": "tsx prisma/seed.ts"}}